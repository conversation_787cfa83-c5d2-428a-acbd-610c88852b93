import {
  json,
  type LoaderFunctionArgs,
  type ActionFunctionArgs,
} from "@remix-run/node";
import { z } from "zod";
import { corsHeaders, handleOptionsRequest } from "./Utility/cors";
import {
  successResponse,
  errorResponse,
  notFoundResponse,
  serverErrorResponse,
} from "./Utility/responses";
// import { responseCache } from "./Utility/cache";
import { getSessionAndSettings } from "./Utility/session";
import { aiProvider } from "./Utility/aiProvider";

// Define interfaces for our data structures
interface MetafieldType {
  namespace?: string;
  key: string;
  value: string;
  type?: string;
}

interface MediaType {
  type: string;
  id: string;
  src: string;
  altText?: string;
}

interface SeoResponseCache {
  success: boolean;
  data: {
    metafields?: MetafieldType[];
    media?: MediaType[];
  };
}

// Custom type for the SettingsInfo we're querying
interface SettingsInfoResult {
  apiKey: string;
  aiModel: string;
  aiModelOptions: string;
  prompt: string;
}

// Zod schema that matches the frontend's structure
const seoRequestSchema = z.object({
  metafields: z
    .array(
      z.object({
        namespace: z.string().optional(),
        key: z.string(),
        value: z.string(),
        type: z.string().optional(),
      }),
    )
    .optional(),
  media: z
    .array(
      z.object({
        altText: z.string().optional(),
        id: z.string(),
        imageId: z.string(),
        type: z.string(),
        src: z.string(),
      }),
    )
    .optional(),
  title: z.string().optional(),
  description: z.string().optional(),
  instructions: z.string().optional(),
  isRegeneration: z.boolean().optional().default(false),
  shopUrl: z.string().optional(),
});

//TODO: Define schemas for structured output with Zod
const seoMetafieldsSchema = z.object({
  metafields: z.array(
    z.object({
      key: z.string(),
      value: z.string(),
    }),
  ),
});

const altTagsSchema = z.object({
  media: z.array(
    z.object({
      altText: z.string(),
      id: z.string(),
      imageId: z.string(),
      type: z.string(),
      src: z.string(),
    }),
  ),
});

export const loader = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "OPTIONS") {
    return handleOptionsRequest();
  }

  return json(
    { message: "SEO API endpoint is ready" },
    {
      status: 200,
      headers: corsHeaders,
    },
  );
};

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    // Parse and validate the incoming request
    const requestData = seoRequestSchema.parse(await request.json());
    const {
      metafields,
      media,
      title,
      description,
      instructions,
      shopUrl,
      isRegeneration = false,
    } = requestData;

    // Determine which type of request this is (metafields or alt tags)
    const isMetafieldsRequest = !!metafields;
    const isMediaRequest = !!media;

    if (!isMetafieldsRequest && !isMediaRequest) {
      return errorResponse(
        "Request must include either metafields or media",
        400,
      );
    }

    // Generate cache key based on request type and content
    const cacheKey = JSON.stringify({
      title,
      description,
      instructions,
      isMetafieldsRequest,
      isMediaRequest,
      isRegeneration,
      metafields: isMetafieldsRequest ? metafields : undefined,
      media: isMediaRequest ? media : undefined,
    });

    // Only use caching for non-regeneration requests
    // if (!isRegeneration) {
    //   const cachedResponse = responseCache.get(cacheKey);
    //   if (cachedResponse) {
    //     return successResponse(cachedResponse);
    //   }
    // }

    const { session, settings } = await getSessionAndSettings(shopUrl || "");

    // Check if the session exists
    if (!session) {
      return notFoundResponse("Shop not found");
    }

    // Check if the settings info exists
    if (!settings) {
      return notFoundResponse("Settings not found");
    }

    let result;

    if (isMetafieldsRequest && metafields) {
      // Handle SEO metafields generation
      result = await generateSeoMetafields(
        metafields,
        title,
        description,
        instructions,
        isRegeneration,
        settings,
      );
    } else if (isMediaRequest && media) {
      // Handle alt tags generation
      result = await generateAltTags(
        media,
        title,
        description,
        instructions,
        isRegeneration,
        settings,
      );
    } else {
      // Return empty result if neither condition is met
      result = {};
    }

    // Create response object
    const responseObject: SeoResponseCache = {
      success: true,
      data: result,
    };

    // Store in cache if not a regeneration
    // if (!isRegeneration) {
    //   responseCache.set(cacheKey, responseObject);
    // }

    return successResponse(responseObject);
  } catch (error) {
    return serverErrorResponse(error);
  }
};

/**
 * Generate SEO metafields (title and description tags)
 */
async function generateSeoMetafields(
  metafields: MetafieldType[],
  title: string | undefined,
  description: string | undefined,
  instructions: string | undefined,
  isRegeneration: boolean,
  settingsInfo: SettingsInfoResult,
): Promise<{ metafields: MetafieldType[] }> {
  //TODO: Create system prompt for AI
  const systemPrompt = `You are a Shopify SEO expert specializing in e-commerce optimization.
  Your task is to generate high-quality SEO meta title and description tags.
  ${isRegeneration ? "IMPORTANT: This is a regeneration request. You MUST provide completely fresh wording with different phrasing, structure, and approach than the original. DO NOT use similar phrasing." : ""}

  Provide your response as a JSON object with metafields array containing objects with key and value properties.`;

  // Create user prompt
  const userPrompt = `
    Generate optimized SEO metadata for this product:

    Product Title: ${title || ""}
    Product Description: ${description || ""}

    ${instructions ? `Special instructions: ${instructions}` : ""}

    Please generate:
    1. A compelling meta title tag (under 60 characters) that includes relevant keywords
    2. A persuasive meta description (under 155 characters) that encourages clicks

    Current meta tags (for reference):
    ${metafields.map((m) => `${m.key}: ${m.value}`).join("\n")}

    Follow these SEO best practices:
    - Include primary keywords near the beginning
    - Create compelling, action-oriented text
    - Avoid keyword stuffing
    - Make sure the meta title is 50-60 characters
    - Make sure the meta description is 140-155 characters
    `;

  try {
    //TODO: Use the aiProvider with the schema
    const result = await aiProvider(settingsInfo, {
      systemPrompt,
      userPrompt,
      isRegeneration,
      schema: seoMetafieldsSchema,
    });

    // TODO: NEW - Use optional chaining to safely access result.data.metafields
    if (result.success && result.data?.metafields) {
      // Map the generated values back to the original metafields
      const updatedMetafields = metafields.map((field) => {
        const generatedField = result.data.metafields.find(
          (m: { key: string; value: string }) => m.key === field.key,
        );
        return generatedField
          ? { ...field, value: generatedField.value }
          : field;
      });

      return { metafields: updatedMetafields };
    }
  } catch (error) {
    // Error handling for SEO metafields generation
  }

  // If AI generation fails, return the original metafields
  return { metafields };
}

/**
 * Generate alt tags for product images
 */
async function generateAltTags(
  media: MediaType[],
  title: string | undefined,
  description: string | undefined,
  instructions: string | undefined,
  isRegeneration: boolean,
  settingsInfo: SettingsInfoResult,
): Promise<{ media: MediaType[] }> {
  //TODO: Create system prompt for AI
  const systemPrompt = `You are a Shopify e-commerce specialist with expertise in image accessibility and SEO.
    Your task is to generate descriptive and SEO-friendly alt text for product images.
    ${isRegeneration ? "This is a regeneration request - provide fresh, different descriptions than the original." : ""}

    Provide your response as a JSON object with a media array containing objects with type, id, src, and altText properties.`;

  // Create user prompt
  const userPrompt = `
    Generate optimized alt text for product images:

    Product Title: ${title || ""}
    Product Description: ${description || ""}
    Current images:
    ${media.map((m, i) => `Image ${i + 1}: ${m.altText || "[No alt text]"}`).join("\n")}

    Please generate concise yet descriptive alt text for each image that:
    - Accurately describes what's visible in the image
    - Includes relevant product attributes (color, style, etc.)
    - Is readable and conversational (not just keywords)
    - Is under 125 characters per image
    - Considers both SEO and accessibility
    `;

  try {
    //TODO: Use the aiProvider with the schema
    const result = await aiProvider(settingsInfo, {
      systemPrompt,
      userPrompt,
      isRegeneration,
      schema: altTagsSchema,
      imageData: media, // Pass media data for image processing
    });

    if (result.success && result.data.media) {
      const updatedMedia = media.map((originalItem, index) => {
        const generatedItem = result.data.media[index];
        return generatedItem
          ? { ...originalItem, altText: generatedItem.altText }
          : originalItem;
      });

      return { media: updatedMedia };
    }
  } catch (error) {
    // Error handling for alt tags generation
  }

  // If AI generation fails, return the original media
  return { media };
}
