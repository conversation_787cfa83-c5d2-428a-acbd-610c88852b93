import { useEffect, useRef, useState, use<PERSON><PERSON>back, useMemo } from "react";
import {
  reactExtension,
  useApi,
  AdminAction,
  BlockStack,
  Button,
  Text,
  Heading,
  TextArea,
  Divider,
  Image,
  InlineStack,
  TextField,
  Box,
  Banner,
} from "@shopify/ui-extensions-react/admin";
import { z } from "zod";

const seoDataTypes = z.object({
  metafields: z.array(
    z.object({
      namespace: z.string(),
      key: z.string(),
      value: z.string(),
      type: z.string(),
    }),
  ),
  media: z.array(
    z.object({
      type: z.string(),
      id: z.string(),
      src: z.string(),
      altText: z.string(),
    }),
  ),
  title: z.string(),
  description: z.string(),
  instructions: z.string().optional(),
  shopUrl: z.string(),
});

type seoDataType = z.infer<typeof seoDataTypes>;

// Moved chunkArray outside the component to avoid recreating it on every render
function chunkArray<T>(array: T[], size: number): T[][] {
  const chunkedArray: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunkedArray.push(array.slice(i, i + size));
  }
  return chunkedArray;
}

// The target used here must match the target used in the extension's toml file (./shopify.extension.toml)
const TARGET = "admin.product-details.action.render";

export default reactExtension(TARGET, () => <App />);
function App() {
  const [loadingMessage, setLoadingMessage] = useState("");
  const [instructions, setInstructions] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGenerated, setIsGenerated] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastGeneratedType, setLastGeneratedType] = useState<string | null>(
    null,
  );
  // The useApi hook provides access to several useful APIs like i18n, close, and data.
  const { i18n, close, data } = useApi(TARGET);
  const [seoData, setSeoData] = useState<seoDataType>({
    metafields: [],
    media: [],
    title: "",
    description: "",
    shopUrl: "",
  });

  // Memoize mediaRows to avoid recalculating on every render
  const mediaRows = useMemo(
    () => chunkArray(seoData.media, 2),
    [seoData.media],
  );

  // Use direct API calls to fetch data from Shopify.
  // See https://shopify.dev/docs/api/admin-graphql for more information about Shopify's GraphQL API
  useEffect(() => {
    (async function getProductInfo() {
      const getProductQuery = {
        query: `query Product($id: ID!) {
          product(id: $id) {
            title
            description
            metafields(first: 20, namespace: "global") {
              edges {
                node {
                  namespace
                  key
                  value
                  type
                }
              }
            }
          media(first: 20) {
              edges {
                node {
                  __typename
                  id
                  ... on MediaImage {
                    image {
                      id
                      url
                      altText
                    }
                  }
                }
              }
            }
          }
          shop {
            url
            myshopifyDomain
            primaryDomain {
              url
              host
            }
          }
        }`,
        variables: { id: data.selected[0].id },
      };

      const res = await fetch("shopify:admin/api/graphql.json", {
        method: "POST",
        body: JSON.stringify(getProductQuery),
      });

      if (!res.ok) {
        // Handle network error
      }
      const seoData = await res.json();
      // Process metafields
      const metafields = seoData.data.product.metafields.edges
        .filter(
          (edge) =>
            edge.node.key === "title_tag" ||
            edge.node.key === "description_tag",
        )
        .map((edge) => ({
          namespace: edge.node.namespace,
          key: edge.node.key,
          value: edge.node.value,
          type: edge.node.type,
        }));

      //TODO: Process media
      const media = seoData.data.product.media.edges
        .map((edge) => {
          const { node } = edge;
          if (node.__typename === "MediaImage") {
            return {
              type: "image",
              id: node.id, // Use the media node ID instead
              imageId: node.image.id, // Keep image ID if needed elsewhere
              src: node.image.url,
              altText: node.image.altText,
            };
          }
          return null;
        })
        .filter(Boolean);
      // shop url
      const shopUrl = seoData.data.shop.url.replace(
        /^(https?:\/\/)?(www\.)?/,
        "",
      );

      const allFields: seoDataType = {
        metafields,
        media,
        title: seoData.data.product.title,
        description: seoData.data.product.description,
        shopUrl,
      };

      setSeoData(allFields);
    })();
  }, [data.selected]);

  // Factory handler for metafield changes - memoized to avoid recreation
  const handleMetafieldChange = useCallback(
    (key: string) => (value: string) => {
      setSeoData((prev) => ({
        ...prev,
        metafields: prev.metafields.map((m) =>
          m.key === key ? { ...m, value } : m,
        ),
      }));
    },
    [],
  );

  // Factory handler for media alt text changes - memoized to avoid recreation
  const handleMediaAltChange = useCallback(
    (id: string) => (value: string) => {
      setSeoData((prev) => ({
        ...prev,
        media: prev.media.map((m) =>
          m.id === id ? { ...m, altText: value } : m,
        ),
      }));
    },
    [],
  );

  // Function to generate SEO fields - memoized with dependencies
  const seoFieldsGenerator = useCallback(async () => {
    try {
      setError(null);
      setIsGenerating(true);
      setLastGeneratedType("metafields");

      // Prepare request data for the API
      const requestData = {
        metafields: seoData.metafields,
        title: seoData.title,
        description: seoData.description,
        instructions,
        isRegeneration: isGenerated,
        shopUrl: seoData.shopUrl,
      };

      // Send request to your backend API
      const response = await fetch("/api/seo", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error("Failed to generate SEO content");
      }

      const result = await response.json();

      if (result.success) {
        // Update state with the generated content
        setSeoData((prevData) => ({
          ...prevData,
          metafields: result.data.data.metafields,
        }));
        setIsGenerated(true);
      } else {
        throw new Error(result.message || "Failed to generate SEO content");
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "An unknown error occurred",
      );
    } finally {
      setIsGenerating(false);
    }
  }, [seoData, instructions, isGenerated]);

  // Function to generate alt tags - memoized with dependencies
  const altTagGenerator = useCallback(async () => {
    try {
      setError(null);
      setIsGenerating(true);
      setLastGeneratedType("altTags");

      // Prepare request data for the API
      const requestData = {
        media: seoData.media,
        title: seoData.title,
        description: seoData.description,
        isRegeneration: isGenerated,
        shopUrl: seoData.shopUrl,
      };

      // Send request to your backend API
      const response = await fetch("/api/seo", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error("Failed to generate alt tags");
      }

      const result = await response.json();

      if (result.success) {
        // Update state with the generated content
        setSeoData((prevData) => ({
          ...prevData,
          media: result.data.data.media,
        }));
        setIsGenerated(true);
      } else {
        throw new Error(result.message || "Failed to generate alt tags");
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "An unknown error occurred",
      );
    } finally {
      setIsGenerating(false);
    }
  }, [seoData, isGenerated]);

  // Function to save changes - memoized with dependencies
  const saveChanges = useCallback(async () => {
    try {
      setError(null);
      setIsGenerating(true);

      // Only update metafields if they exist and have all required properties
      if (
        seoData.metafields &&
        seoData.metafields.length > 0 &&
        seoData.metafields.every(
          (field) => field.namespace && field.key && field.type,
        )
      ) {
        const metafieldInputs = seoData.metafields.map((field) => ({
          namespace: field.namespace,
          key: field.key,
          value: field.value,
          type: field.type,
        }));

        const updateMetafieldsQuery = {
          query: `mutation productUpdate($input: ProductInput!) {
            productUpdate(input: $input) {
              product {
                id
              }
              userErrors {
                field
                message
              }
            }
          }`,
          variables: {
            input: {
              id: data.selected[0].id,
              metafields: metafieldInputs,
            },
          },
        };

        const metafieldsRes = await fetch("shopify:admin/api/graphql.json", {
          method: "POST",
          body: JSON.stringify(updateMetafieldsQuery),
        });

        if (!metafieldsRes.ok) {
          throw new Error("Failed to update metafields");
        }

        const metafieldsResult = await metafieldsRes.json();

        if (metafieldsResult.data.productUpdate.userErrors.length > 0) {
          throw new Error(
            metafieldsResult.data.productUpdate.userErrors[0].message,
          );
        }
      }

      // Only update media alt texts if they exist
      if (seoData.media && seoData.media.length > 0) {
        for (const media of seoData.media) {
          if (!media.altText) continue;
          const mediaToUpdate = [
            {
              id: media.id,
              alt: media.altText,
            },
          ];

          const updateMediaQuery = {
            query: `mutation UpdateProductMedia($media: [UpdateMediaInput!]!, $productId: ID!) {
              productUpdateMedia(media: $media, productId: $productId) {
                media {
                  id
                  alt
                }
                userErrors {
                  field
                  message
                }
              }
            }`,
            variables: {
              productId: data.selected[0].id,
              media: mediaToUpdate,
            },
          };

          const mediaRes = await fetch("shopify:admin/api/graphql.json", {
            method: "POST",
            body: JSON.stringify(updateMediaQuery),
          });
          if (!mediaRes.ok) {
            throw new Error(`Failed to update alt text for media ${media.id}`);
          }

          const mediaResult = await mediaRes.json();

          // Check if the expected data structure exists before accessing properties
          if (mediaResult.data && mediaResult.data.mediaUpdate) {
            if (
              mediaResult.data.mediaUpdate.mediaUserErrors &&
              mediaResult.data.mediaUpdate.mediaUserErrors.length > 0
            ) {
              // Handle media update errors
            }
          } else if (mediaResult.errors) {
            // Handle GraphQL errors
          } else {
            // Handle unexpected response format
          }
        }
      }

      // Close the extension after successful save
      close();
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "An unknown error occurred",
      );
    } finally {
      setIsGenerating(false);
    }
  }, [seoData, data.selected, close]);

  // UI part stays mostly the same, just update the handlers
  return (
    <AdminAction
      primaryAction={<Button onPress={saveChanges}>Publish</Button>}
      secondaryAction={
        <Button
          onPress={() => {
            close();
          }}
        >
          Close
        </Button>
      }
    >
      <BlockStack paddingBlockEnd="large">
        <Heading size={3}>
          {seoData.metafields.length === 0 ? "" : "Edit SEO Data"}
        </Heading>
        {seoData.metafields.map((metafield) => (
          <BlockStack key={metafield.key} paddingBlockStart="large">
            <TextArea
              label={`${metafield.key
                .split("_")
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" ")}`}
              value={metafield.value}
              onChange={handleMetafieldChange(metafield.key)}
            />
          </BlockStack>
        ))}
        {/* Instruction input field */}
        <BlockStack paddingBlockStart="large">
          <TextArea
            label="AI Instructions"
            value={instructions}
            onChange={(value) => setInstructions(value)}
            placeholder="Enter instructions for AI content generation (e.g., 'Make it more persuasive' or 'Emphasize product benefits')"
          />
        </BlockStack>
        <Divider />

        {/* Error banner */}
        {error && (
          <Banner tone="critical">
            <Text>{error}</Text>
          </Banner>
        )}

        {/* Loading state indicator */}
        {isGenerating && (
          <Banner tone="info">
            <InlineStack gap="base">
              <Text>{loadingMessage || "Generating content..."}</Text>
            </InlineStack>
          </Banner>
        )}

        {/* Success message */}
        {loadingMessage && !isGenerating && !error && (
          <Banner tone="success">
            <Text>{loadingMessage}</Text>
          </Banner>
        )}
        <Divider />
        <BlockStack paddingBlockStart="large">
          <Button
            onPress={seoFieldsGenerator}
            disabled={isGenerating}
            variant="primary"
          >
            {lastGeneratedType === "metafields"
              ? "Re-Generate SEO Fields"
              : "Generate SEO Fields"}
          </Button>
        </BlockStack>
      </BlockStack>
      <Divider />
      <BlockStack gap="base" paddingBlockStart="large">
        <Heading size={3}>Media</Heading>
        {mediaRows.map((row, idx) => (
          <InlineStack key={idx} gap="base">
            {row.map((media) => (
              <BlockStack key={media.id} gap="base large">
                <Image src={media.src} alt={media.altText} />
                <TextField
                  label="Alt Text"
                  value={media.altText || "you don't have alt text"}
                  placeholder="Enter alt text"
                  onChange={handleMediaAltChange(media.id)}
                />
                <Divider />
              </BlockStack>
            ))}
          </InlineStack>
        ))}
      </BlockStack>
      <Divider />

      {/* Error banner */}
      {error && (
        <Banner tone="critical">
          <Text>{error}</Text>
        </Banner>
      )}

      {/* Loading state indicator */}
      {isGenerating && (
        <Banner tone="info">
          <InlineStack gap="base">
            <Text>{loadingMessage || "Generating content..."}</Text>
          </InlineStack>
        </Banner>
      )}

      {/* Success message */}
      {loadingMessage && !isGenerating && !error && (
        <Banner tone="success">
          <Text>{loadingMessage}</Text>
        </Banner>
      )}

      <Divider />

      <BlockStack paddingBlockStart="large">
        <Button
          onPress={altTagGenerator}
          disabled={isGenerating}
          variant="primary"
        >
          {lastGeneratedType === "altTags"
            ? "Re-Generate Alt Tags"
            : "Generate Alt Tags"}
        </Button>
      </BlockStack>
    </AdminAction>
  );
}
