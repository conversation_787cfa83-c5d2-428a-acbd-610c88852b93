import prisma from "app/db.server";

/**
 * Get a session by shop URL
 */
export async function getSessionByShop(shopUrl: string) {
  try {
    return await prisma.session.findFirst({
      where: { shop: shopUrl },
      select: { id: true },
    });
  } catch (error) {
    return null;
  }
}

/**
 * Get both session and settings in one query
 */
export async function getSessionAndSettings(shopUrl: string) {
  try {
    const [session, settings] = await Promise.all([
      prisma.session.findFirst({
        where: { shop: shopUrl },
        select: { id: true },
      }),
      prisma.settingsInfo.findFirst({
        where: {
          session: {
            shop: shopUrl,
          },
        },
        select: {
          apiKey: true,
          aiModel: true,
          aiModelOptions: true,
          prompt: true,
          onboardingCompleted: true,
        },
      }),
    ]);
    return { session, settings };
  } catch (error) {
    return { session: null, settings: null };
  }
}

/**
 * Get AI settings for a shop
 */
export async function getAISettings(shopUrl: string) {
  try {
    const settings = await prisma.settingsInfo.findFirst({
      where: {
        session: {
          shop: shopUrl,
        },
      },
      select: {
        apiKey: true,
        aiModel: true,
        aiModelOptions: true,
        prompt: true,
      },
    });

    return settings;
  } catch (error) {
    return null;
  }
}
