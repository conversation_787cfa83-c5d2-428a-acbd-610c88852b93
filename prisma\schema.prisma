// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("SHOPIFY_DB")
}

model Session {
  id            String        @id
  shop          String
  state         String
  isOnline      Boolean       @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  <PERSON><PERSON><PERSON>       @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?      @default(false)
  emailVerified <PERSON>ole<PERSON>?      @default(false)
  SettingsInfo  SettingsInfo?
  productInfo   ProductInfo[]
}

model SettingsInfo {
  id                  String   @id @unique @default(uuid())
  aiModel             String
  apiKey              String
  aiModelOptions      String
  prompt              String
  sessionId           String?  @unique
  onboardingCompleted Boolean  @default(false)
  session             Session? @relation(fields: [sessionId], references: [id])
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
}

model ProductInfo {
  id          String   @id @unique @default(uuid())
  productId   String   @unique
  title       String
  description String
  image       Json
  mataField   Json
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  sessionId   String?
  session     Session? @relation(fields: [sessionId], references: [id])

  @@index([productId])
  @@index([sessionId])
}
