import { json } from "@remix-run/node";
import { corsHeaders } from "./cors";

/**
 * Create a success response with standard format
 */
export function successResponse(
  data: any,
  message: string = "Success from response.ts",
  status: number = 200,
) {
  return json(
    {
      success: true,
      message,
      data,
    },
    {
      status,
      headers: corsHeaders,
    },
  );
}

/**
 * Create an error response with standard format
 */
export function errorResponse(message: string, status: number = 400) {
  return json(
    {
      success: false,
      message,
    },
    {
      status,
      headers: corsHeaders,
    },
  );
}

/**
 * Create a not found response
 */
export function notFoundResponse(message: string = "Resource not found") {
  return errorResponse(message, 404);
}

/**
 * Create a server error response
 */
export function serverErrorResponse(error: unknown) {
  const errorMessage =
    error instanceof Error ? error.message : "Unknown error occurred";
  console.error("Server error:", error);

  return errorResponse(errorMessage, 500);
}
