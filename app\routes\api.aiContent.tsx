import {
  json,
  type LoaderFunctionArgs,
  type ActionFunctionArgs,
} from "@remix-run/node";
//TODO: Import aiProvider and z for schema definition
import { z } from "zod";
import { aiProvider } from "./Utility/aiProvider";
import prisma from "app/db.server";
import { corsHeaders, handleOptionsRequest } from "./Utility/cors";
import {
  successResponse,
  errorResponse,
  notFoundResponse,
  serverErrorResponse,
} from "./Utility/responses";
// import { responseCache } from "./Utility/cache";
import { getSessionAndSettings, getAISettings } from "./Utility/session";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "OPTIONS") {
    return handleOptionsRequest();
  }

  return json(
    { message: "Hello from AI Content Generation" },
    {
      status: 200,
      headers: corsHeaders,
    },
  );
};

//TODO: Define schema for structured content generation
const contentGenerationSchema = z.object({
  title: z.string(),
  description: z.string(),
  metafields: z.record(z.string(), z.string()).or(
    z.array(
      z.object({
        key: z.string(),
        value: z.string(),
      }),
    ),
  ),
});

// Consolidated action handler for both initial generation and regeneration
export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const requestData = await request.json();
    const {
      isRegeneration,
      shopUrl,
      instructions,
      currentTitle,
      currentContent,
      currentMataField,
      productId, // The Shopify product ID
    } = requestData;

    // Only use caching for non-regeneration requests
    // if (!isRegeneration) {
    //   // Generate a cache key from the request parameters
    //   const cacheKey = JSON.stringify({
    //     shopUrl,
    //     instructions,
    //     productId,
    //   });

    //   // Check for cached response
    //   const cachedResponse = responseCache.get(cacheKey);
    //   if (cachedResponse) {
    //     return successResponse(cachedResponse);
    //   }
    // }

    // For improved performance, fetch shop and settings in parallel
    const { session, settings } = await getSessionAndSettings(shopUrl);

    // Check if the session exists
    if (!session) {
      return notFoundResponse("Shop not found");
    }

    // Check if the settings info exists
    if (!settings) {
      return notFoundResponse("Settings not found");
    }

    // Prepare product data
    let title, description, image, mataField;

    if (isRegeneration) {
      // For regeneration, use the data provided from the frontend
      title = currentTitle;
      description = currentContent;
      // Use provided mataField or default to empty object
      mataField = currentMataField || {};
      // Default empty data for optional fields - ensure image is always an array
      image = Array.isArray(requestData.image) ? requestData.image : [];
    } else {
      // For initial generation, fetch the product from database
      if (!productId) {
        return errorResponse(
          "Product ID is required for initial generation",
          400,
        );
      }

      // Get product info with proper field selection
      const productInfo = await prisma.productInfo.findUnique({
        where: {
          productId: productId,
        },
        select: {
          title: true,
          description: true,
          image: true,
          mataField: true,
        },
      });

      if (!productInfo) {
        return notFoundResponse("Product not found");
      }

      // Extract only what we need
      title = productInfo.title;
      description = productInfo.description;
      // Ensure image is always an array
      image = Array.isArray(productInfo.image) ? productInfo.image : [];
      mataField = productInfo.mataField;
    }

    let enhancedTitle = title;
    let enhancedDescription = description;
    let enhancedMataField = mataField;

    try {
      //TODO: Replace direct OpenAI call with aiProvider utility
      // Create a compact system prompt
      const systemPrompt = `You are a Shopify product content generator that specializes in creating SEO-optimized product descriptions based primarily on product images provided via image URLs.

      Your task is to carefully analyze the visual content in the provided image(s) and generate a high-quality product description that accurately reflects what is shown. Use visual cues (e.g., product type, design features, material, use case, color, style) to extract meaningful information, and create engaging content optimized for both SEO and user experience.

      Follow these content and formatting guidelines:

      1. Image-Based Analysis
      - Rely on the visual content of the product image(s) to infer product features.

      - Extract observable details such as material, shape, use case, functionality, size cues, style, and color.

      - If an image lacks clarity or sufficient context, use best-guess reasoning based on typical product conventions—but avoid making unrealistic claims.

      2. Writing Style and Tone
      - Use a conversational yet professional tone.

      - Write in clear, concise language suitable for an average consumer (7th–9th grade reading level).

      - Use active voice and informative phrasing.

      - Avoid jargon unless it’s necessary and explained.

      3. Structure and Formatting
      - Output must be written in clean, valid HTML.

      - Begin with a short introductory paragraph (do not start with bullet points).

      - Use semantic heading tags: <h2>, <h3>, and <h4> to organize sections.

      - Break up content into logically grouped sections (e.g., Features, Benefits, How to Use, etc.).

      - Use <ul> and <ol> lists only after the introduction, where appropriate.

      - Highlight key features or selling points using <strong> (bold).

      - Keep paragraphs short (2–4 sentences) to improve readability.

      - Ensure consistent use of line breaks and spacing for clean formatting.
      - don't use <html>, <body>, <h1>, <section>, <div> etc your are writing content.

      4. SEO and Keyword Guidelines
      - Integrate relevant keywords naturally throughout the content based on visual context and likely product search intent.

      - Do not keyword-stuff or repeat phrases unnecessarily.

      - Use descriptive headings and maintain a logical flow to support both SEO and user engagement.

      - Always format the entire response in HTML. Never begin the content with a list. Start with a short summary paragraph, followed by organized sections with proper headings and lists.

      - Let me know if you’d like a version tailored to specific verticals (fashion, electronics, home goods, etc.) or want fallback logic when multiple products are shown in one image.

        ${isRegeneration ? "This is a regeneration request - provide fresh, different wording than the original." : ""}

        Return a JSON object with title, description, and only the ${mataField ? JSON.stringify(mataField).substring(0, 300) : "No metafields available"} that we have properties. If No metafields avilable return an empty object.`;

      // Create user prompt
      const userPrompt = `
        As an expert content writer with deep understanding of product photography, I need you to analyze this product and generate optimized content.

        CRITICAL: ANALYZE THE PRODUCT IMAGES CAREFULLY - they may contain the main product information!
        Product images information:
        ${Array.isArray(image) ? image.map((m: { src: string }, i: number) => `Image ${i + 1}: ${m.src}`).join("\n") : "No images available"}

        Available product text information:
        Title: ${title || "No title available"}
        Description: ${description || "No description available"}
        Metafields: ${mataField ? JSON.stringify(mataField).substring(0, 300) : "No metafields available"}

        ${settings?.prompt ? `Shop context: ${settings.prompt}` : ""}

        Instructions from user: ${instructions}
        ${isRegeneration ? "This is a regeneration - use a completely different approach." : ""}

        IMPORTANT DIRECTIONS:
        1. Look carefully at image URLs and alt texts to understand the product
        2. If description is missing or minimal, rely heavily on image analysis
        3. Extract product features, colors, materials, and use cases from images
        4. Generate an enhanced title, description, and metafields that are SEO-friendly
        5. Ensure your content reflects what's actually visible in the product images

        Even if there's limited text data, provide comprehensive content based on what you can discern from the images.
        `;

      // Use the aiProvider with the schema and image data
      const result = await aiProvider(settings, {
        systemPrompt,
        userPrompt,
        isRegeneration,
        schema: contentGenerationSchema,
        imageData: image, // Pass the image data for processing
      });

      // TODO: NEW - Fix TypeScript error by properly checking result.data exists
      if (result.success && result.data) {
        // Extract the enhanced content
        enhancedTitle = result.data.title || title;
        enhancedDescription = result.data.description || description;

        // Handle metafields
        if (result.data.metafields) {
          // Start with existing metafields structure
          let updatedMetafields = [...mataField];

          // If AI returned metafields as an object
          if (
            typeof result.data.metafields === "object" &&
            !Array.isArray(result.data.metafields)
          ) {
            // Only update existing metafields, don't add new ones
            updatedMetafields.forEach((field, index) => {
              // Check if this key exists in the AI response
              if (result.data.metafields[field.key]) {
                // Update only the value
                updatedMetafields[index].value =
                  result.data.metafields[field.key];
              }
            });
          }
          // If AI returned metafields already as an array
          else if (Array.isArray(result.data.metafields)) {
            // Map through existing metafields and update values
            updatedMetafields = updatedMetafields.map((field) => {
              const aiField = result.data.metafields.find(
                (m: { key: string; value: string }) => m.key === field.key,
              );
              return aiField ? { ...field, value: aiField.value } : field;
            });
          }

          enhancedMataField = updatedMetafields;
        }
      }
    } catch (aiError) {
      console.error("AI generation error:", aiError);
      // Continue with original content
    }

    // Create the response object with minimal data
    const responseObject = {
      success: true,
      data: {
        content: enhancedDescription, // Match frontend expected field name
        title: enhancedTitle,
        mataField: enhancedMataField, // Include the enhanced metafields
        // Only return minimal settings data needed
        settings: {
          aiModel: settings.aiModel,
        },
        isRegeneration,
      },
    };

    // Store in cache if not a regeneration
    // if (!isRegeneration) {
    //   const cacheKey = JSON.stringify({
    //     shopUrl,
    //     instructions,
    //     productId,
    //   });

    //   responseCache.set(cacheKey, responseObject);
    // }

    return successResponse(responseObject);
  } catch (error) {
    console.error("Error in action function:", error);
    return serverErrorResponse(error);
  }
};
