import { useEffect, useState, useCallback, useMemo, useRef } from "react";
import {
  reactExtension,
  useApi,
  AdminAction,
  BlockStack,
  Button,
  Heading,
  Divider,
  Text,
  InlineStack,
  TextArea,
  Section,
  TextField,
  Banner,
} from "@shopify/ui-extensions-react/admin";

// The target used here must match the target used in the extension's toml file (./shopify.extension.toml)
const TARGET = "admin.product-details.action.render";

export default reactExtension(TARGET, () => <App />);

function App() {
  // The useApi hook provides access to several useful APIs like i18n, close, and data.
  const { i18n, close, data } = useApi(TARGET);

  // Product content state
  const [userContent, setUserContent] = useState({
    id: "",
    title: "",
    shopUrl: "",
    content: "",
    media: [],
    metafields: [],
  });

  // Track generation states and content
  const [isGenerated, setIsGenerated] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [instructions, setInstructions] = useState("");
  const [loadingMessage, setLoadingMessage] = useState("");
  const [error, setError] = useState(null);
  const activeRequestRef = useRef(false);

  // State to track when to trigger content generation
  const [shouldGenerate, setShouldGenerate] = useState(false);
  const [isRegeneration, setIsRegeneration] = useState(false);
  const [abortController, setAbortController] = useState(null);

  // Memoize metafields for performance
  const metafieldInputs = useMemo(() => {
    return userContent.metafields.map((metafield) => ({
      namespace: "custom",
      key: metafield.key,
      value:
        typeof metafield.value === "object"
          ? JSON.stringify(metafield.value)
          : metafield.value,
      type: metafield.type || "single_line_text_field", // Fallback type if missing
      ownerId: `gid://shopify/Product/${userContent.id}`,
    }));
  }, [userContent.metafields, userContent.id]);

  // Use direct API calls to fetch data from Shopify.
  useEffect(() => {
    if (!data.selected || !data.selected.length) return;

    const getProductInfo = async () => {
      const getProductQuery = {
        query: `
        query Product($id: ID!) {
          product(id: $id) {
            title
            description
            media(first: 10) {
              edges {
                node {
                  __typename
                  ... on MediaImage {
                    image {
                      id
                      url
                      altText
                    }
                  }
                }
              }
            }
            metafields(first: 20 namespace: "custom") {
              edges {
                node {
                  namespace
                  key
                  value
                  type
                }
              }
            }
          }
          shop {
            url
            myshopifyDomain
            primaryDomain {
              url
              host
            }
          }
        }`,
        variables: { id: data.selected[0].id },
      };

      try {
        const res = await fetch("shopify:admin/api/graphql.json", {
          method: "POST",
          body: JSON.stringify(getProductQuery),
        });

        if (!res.ok) {
          setError("Network error");
          return;
        }

        const productData = await res.json();
        const { title, description } = productData.data.product;

        // Process media
        const media = productData.data.product.media.edges
          .map((edge) => {
            const { node } = edge;
            if (node.__typename === "MediaImage") {
              return {
                type: "image",
                id: node.image.id,
                src: node.image.originalSrc || node.image.url,
                altText: node.image.altText,
              };
            }
            return null;
          })
          .filter(Boolean);

        // Process metafields - more carefully handle types
        const metafields = productData.data.product.metafields.edges
          .filter(
            (edge) =>
              edge.node.type === "single_line_text_field" ||
              edge.node.type === "multi_line_text_field",
          )
          .map((edge) => {
            // Make sure we're getting string values
            let value = edge.node.value;
            try {
              // If it's a JSON string, parse it to see if it's valid
              // If not, keep the original string
              const parsed = JSON.parse(value);
              // If it parsed as an object or array, stringify it back for display
              if (typeof parsed === "object" && parsed !== null) {
                value = JSON.stringify(parsed);
              }
            } catch (e) {
              // Not JSON, leave as is
            }

            return {
              key: `${edge.node.key}`,
              value: value,
              type: edge.node.type,
            };
          });

        // Extract product ID
        const id = data.selected[0].id.split("/").pop().split("?")[0].trim();
        const shopUrl = productData.data.shop.url.replace(
          /^(https?:\/\/)?(www\.)?/,
          "",
        );

        // Create new content object
        const newContent = {
          id,
          shopUrl,
          title,
          content: description,
          media,
          metafields,
        };

        // Set initial content state
        setUserContent(newContent);

        // Save to backend
        saveToBackend(newContent);
      } catch (error) {
        setError("Failed to load product data");
      }
    };

    getProductInfo();
  }, [data.selected]);

  // Extract backend save logic to its own function with useCallback
  const saveToBackend = useCallback(async (content) => {
    try {
      const response = await fetch("/api/productData", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(content),
      });

      if (!response.ok) {
        return;
      }

      const productInfo = await response.json();
    } catch (error) {
      // Handle error saving to backend
    }
  }, []);

  // Function to trigger content generation - optimized with useCallback
  const generateContent = useCallback(() => {
    if (!isGenerating) {
      // Important: Use a function update to ensure we're using the latest state
      setIsRegeneration((current) => isGenerated);
      setShouldGenerate(true);
      setError(null);
    }
  }, [isGenerating, isGenerated]);

  // Handle content field change
  const handleContentChange = useCallback((value) => {
    setUserContent((prev) => ({
      ...prev,
      content: value,
    }));
  }, []);

  // Handle metafield change
  const handleMetafieldChange = useCallback((key, value) => {
    setUserContent((prev) => {
      const updatedMetafields = [...prev.metafields];
      const index = updatedMetafields.findIndex((m) => m.key === key);
      if (index !== -1) {
        updatedMetafields[index] = {
          ...updatedMetafields[index],
          value,
        };
      }
      return {
        ...prev,
        metafields: updatedMetafields,
      };
    });
  }, []);

  // Handle API call in useEffect
  useEffect(() => {
    // Return early if we shouldn't generate
    if (!shouldGenerate || activeRequestRef.current) return;

    // Loading messages array
    const loadingMessages = [
      "Analyzing your product...",
      "Crafting compelling content...",
      "Polishing your description...",
      "Almost there...",
    ];

    let messageIndex = 0;
    const messageInterval = setInterval(() => {
      setLoadingMessage(loadingMessages[messageIndex % loadingMessages.length]);
      messageIndex++;
    }, 2000);

    // Create a new controller for this generation
    const controller = new AbortController();
    setAbortController(controller);
    setIsGenerating(true);
    setLoadingMessage(loadingMessages[0]);

    // Use a separate async function to avoid React's warnings about async useEffect
    const fetchContent = async () => {
      try {
        // Use a single endpoint for both cases
        const endpoint = "/api/aiContent";
        activeRequestRef.current = true;
        // Prepare payload with all possible properties
        const payload: {
          instructions: string;
          isRegeneration: boolean;
          shopUrl: string;
          productId: string;
          currentTitle?: string;
          currentContent?: string;
          currentMataField?: Record<string, string>;
        } = {
          instructions,
          isRegeneration,
          shopUrl: userContent.shopUrl,
          productId: userContent.id,
        };

        // Only include additional data for regeneration
        if (isRegeneration) {
          payload.currentTitle = userContent.title;
          payload.currentContent = userContent.content;

          // Convert metafields to an object - handle possible object values
          payload.currentMataField = userContent.metafields.reduce(
            (acc, field) => {
              let value = field.value;
              // Make sure we're sending a string, not an object
              if (typeof value === "object") {
                value = JSON.stringify(value);
              }
              acc[field.key] = value;
              return acc;
            },
            {},
          );
        }

        // Create a timeout for UX feedback
        const timeoutId = setTimeout(() => {
          setLoadingMessage(
            "This is taking longer than expected, still working on it...",
          );
        }, 8000);

        // Make API call with abort controller
        const response = await fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        // Process successful response
        const result = await response.json();

        if (result.success) {
          // Handle result.data vs result.data.data structure differences between versions
          const generatedContent = result.data.data || result.data;

          // Update the userContent with the generated content
          setUserContent((prev) => {
            const updatedContent = {
              ...prev,
              content: generatedContent.content || prev.content,
              title: generatedContent.title || prev.title,
            };

            // If we received updated metafields, process them
            if (generatedContent.mataField) {
              const updatedMetafields = [...prev.metafields];

              // Handle both array and object format for metafields
              if (Array.isArray(generatedContent.mataField)) {
                generatedContent.mataField.forEach((newField) => {
                  const index = updatedMetafields.findIndex(
                    (field) => field.key === newField.key,
                  );
                  if (index !== -1) {
                    updatedMetafields[index] = {
                      ...updatedMetafields[index],
                      value: newField.value,
                    };
                  }
                });
              } else {
                // Handle object format
                Object.entries(generatedContent.mataField).forEach(
                  ([key, value]) => {
                    const index = updatedMetafields.findIndex(
                      (field) => field.key === key,
                    );
                    if (index !== -1) {
                      updatedMetafields[index] = {
                        ...updatedMetafields[index],
                        value,
                      };
                    }
                  },
                );
              }

              return { ...updatedContent, metafields: updatedMetafields };
            }

            return updatedContent;
          });

          // Mark content as generated
          setIsGenerated(true);
          setLoadingMessage("Content generated successfully!");
          setTimeout(() => setLoadingMessage(""), 2000);
        } else {
          setError(result.message || "Failed to generate content");
        }
      } catch (error) {
        if (error.name === "AbortError") {
          setError("Generation was canceled");
        } else {
          setError("Error generating content. Please try again.");
        }
      } finally {
        clearInterval(messageInterval);
        setIsGenerating(false);
        setShouldGenerate(false);
        setAbortController(null);
        activeRequestRef.current = false;
      }
    };

    fetchContent();

    // Clean up on unmount or when dependencies change
    return () => {
      clearInterval(messageInterval);
      if (activeRequestRef.current && abortController) {
        abortController.abort();
      }
    };
  }, [
    shouldGenerate,
    isRegeneration,
    userContent.shopUrl,
    userContent.id,
    userContent.title,
    userContent.content,
    instructions,
  ]);

  // Undo function to reset content to original state
  const undo = useCallback(async () => {
    try {
      const response = await fetch("/api/undo", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: userContent.id,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch original content");
      }

      const result = await response.json();

      // Set the original content back to the userContent state
      setUserContent((prev) => ({
        ...prev,
        content: result.data.content,
        title: result.data.title,
      }));
      setIsGenerated(false);
    } catch (error) {
      setError("Failed to restore original content");
    }
  }, [userContent.id]);

  // Publish function for saving content and metafields
  const handlePublish = useCallback(async () => {
    try {
      // Format content as HTML if needed
      let processedContent = userContent.content;
      if (
        processedContent &&
        !processedContent.includes("<") &&
        !processedContent.includes(">")
      ) {
        processedContent = `<p>${processedContent}</p>`;
      }

      // Updated GraphQL mutation
      const response = await fetch("shopify:admin/api/graphql.json", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: `mutation UpdateProductWithMetafields($input: ProductInput!, $metafields: [MetafieldsSetInput!]!) {
            productUpdate(input: $input) {
              product { id title descriptionHtml }
              userErrors { field message }
            }
            metafieldsSet(metafields: $metafields) {
              metafields {
                namespace
                key
                value
                type
              }
              userErrors {
                field
                message
              }
            }
          }`,
          variables: {
            input: {
              id: `gid://shopify/Product/${userContent.id}`,
              title: userContent.title,
              descriptionHtml: processedContent,
            },
            metafields: metafieldInputs,
          },
        }),
      });

      const result = await response.json();

      // Check for errors in the response
      const productErrors = result.data?.productUpdate?.userErrors || [];
      const metafieldErrors = result.data?.metafieldsSet?.userErrors || [];

      if (productErrors.length > 0 || metafieldErrors.length > 0) {
        throw new Error("Error saving product or metafields");
      }
    } catch (error) {
      setError("Failed to save product. Please try again.");
      // Don't close on error
      return false;
    }

    // Return true to indicate success (will close the modal)
    return true;
  }, [userContent, metafieldInputs]);

  // Memoize the component for metafields to avoid unnecessary re-renders
  const MetafieldsList = useMemo(() => {
    if (userContent.metafields.length === 0) return null;

    return (
      <>
        <Divider />
        <Heading size={3}>Your Custom Metafields</Heading>
        <BlockStack gap="large base">
          {userContent.metafields.map((metafield) => {
            // Skip rendering if the key is numeric (likely an index)
            if (!isNaN(metafield.key)) return null;

            return (
              <Section key={metafield.key}>
                <BlockStack gap="small base">
                  <TextField
                    label={`${metafield.key
                      .split("_")
                      .map(
                        (word) => word.charAt(0).toUpperCase() + word.slice(1),
                      )
                      .join(" ")} Enter Here`}
                    value={
                      typeof metafield.value === "string"
                        ? metafield.value
                        : JSON.stringify(metafield.value)
                    }
                    onChange={(value) =>
                      handleMetafieldChange(metafield.key, value)
                    }
                  />
                </BlockStack>
              </Section>
            );
          })}
        </BlockStack>
      </>
    );
  }, [userContent.metafields, handleMetafieldChange]);

  return (
    <AdminAction
      primaryAction={
        <Button
          onPress={async () => {
            const success = await handlePublish();
            if (success) close();
          }}
        >
          Publish
        </Button>
      }
      secondaryAction={
        <Button
          onPress={() => {
            close();
          }}
        >
          Close
        </Button>
      }
    >
      <BlockStack gap="large">
        <Heading size={2}>{userContent.title}</Heading>

        {/* Product content display/edit area */}
        <TextArea
          label="Product Description"
          value={userContent.content}
          rows={10}
          onChange={handleContentChange}
          placeholder="Enter product description here..."
        />

        {/* Metafields display/edit area */}
        {MetafieldsList}

        <Divider />

        {/* Instruction input field */}
        <TextArea
          label="AI Instructions"
          value={instructions}
          onChange={setInstructions}
          placeholder="Enter instructions for AI content generation (e.g., 'Make it more persuasive' or 'Emphasize product benefits')"
        />
        <Divider />

        {/* Error banner */}
        {error && (
          <Banner tone="critical">
            <Text>{error}</Text>
          </Banner>
        )}

        {/* Loading state indicator */}
        {isGenerating && (
          <Banner tone="info">
            <InlineStack gap="base">
              <Text>{loadingMessage || "Generating content..."}</Text>
            </InlineStack>
          </Banner>
        )}

        {/* Success message */}
        {loadingMessage && !isGenerating && !error && (
          <Banner tone="success">
            <Text>{loadingMessage}</Text>
          </Banner>
        )}

        <Divider />

        <InlineStack gap="large base">
          <Button
            onPress={generateContent}
            disabled={isGenerating}
            variant="primary"
          >
            {isGenerated ? "Re-Generate Content" : "Generate Content"}
          </Button>

          {/* Only show Undo button after content has been generated */}
          {isGenerated && <Button onPress={undo}>Back to Original</Button>}
        </InlineStack>
      </BlockStack>
    </AdminAction>
  );
}
