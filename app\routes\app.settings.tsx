import {
  Form,
  json,
  useLoaderData,
  useNavigation,
  useSubmit,
  useActionData,
} from "@remix-run/react";
import {
  Box,
  Page,
  Text,
  Select,
  Layout,
  Card,
  BlockStack,
  TextField,
  Button,
} from "@shopify/polaris";
import prisma from "app/db.server";
import { authenticate } from "app/shopify.server";
import { useState, useEffect, useCallback, useMemo } from "react";
import { z } from "zod";
import Onboarding from "./components/Onboarding"; // Import the new component

// Define form schema with zod
const settingFormSchema = z.object({
  aiModel: z.string(),
  apiKey: z.string().min(1, "API Key is required"),
  prompt: z.string().min(1, "Prompt is required"),
  aiModelOptions: z.string().optional(),
});

type SettingFormSchema = z.infer<typeof settingFormSchema>;

// Define return types for our validation function
type ValidationError = {
  success: false;
  error: string;
  message: string;
};

type ValidationSuccess = {
  success: true;
  data: {
    aiModel: string;
    apiKey: string;
    prompt: string;
    aiModelOptions?: string;
  };
};

type ValidationResult = ValidationError | ValidationSuccess;

// AI model options
const aiModleSelector = [
  { label: "Select an option", value: "" },
  { label: "OpenAI", value: "openai" },
  { label: "Claude", value: "claude" },
  { label: "Gemini", value: "gemini" },
];

const keyLabels = {
  openai: "Enter your OpenAI API Key",
  claude: "Enter your Claude API Key",
  gemini: "Enter your Gemini API Key",
};

const aiModelOptions = {
  openai: [
    { label: "Select Model", value: "" },
    { label: "GPT-4.1", value: "gpt-4.1" },
    { label: "GPT-4.1 Mini", value: "gpt-4.1-mini" },
    { label: "GPT-4.1 Nano", value: "gpt-4.1-nano" },
    { label: "GPT-4o", value: "gpt-4o" },
    { label: "GPT-4o Mini", value: "gpt-4o-mini" },
  ],
  claude: [
    { label: "Select Model", value: "" },
    { label: "Claude 3.7 Sonnet", value: "claude-3-7-sonnet-20250219" },
    { label: "Claude 3.5 Sonnet", value: "claude-3-5-sonnet-20241022" },
    { label: "Claude 3 Sonnet", value: "claude-3-sonnet-20240229" },
  ],
  gemini: [
    { label: "Select Model", value: "" },
    { label: "Gemini 2.0 Flash", value: "gemini-2.0-flash" },
    { label: "Gemini 1.5 Pro", value: "gemini-1.5-pro" },
    { label: "Gemini 1.5 Flash", value: "gemini-1.5-flash" },
  ],
};

// Remix action function to handle form submission
export async function action({ request }: { request: Request }) {
  const { session } = await authenticate.admin(request);

  if (!session) {
    return json({
      success: false,
      error: "Not authenticated",
      message: "Authentication failed. Please try again.",
    });
  }

  const formData = await request.formData();
  const intent = formData.get("intent");

  // Handle onboarding completion only (no form data)
  if (intent === "complete-onboarding") {
    // Create empty form data with default values
    const emptyFormData = {
      aiModel: "",
      apiKey: "",
      prompt: "",
      aiModelOptions: "",
    };

    // Use our helper function with completeOnboarding set to true
    const result = await saveSettings(session.id, emptyFormData, true);
    return json(result);
  }

  // Helper function to save settings
  async function saveSettings(
    sessionId: string,
    formData: {
      aiModel: string;
      apiKey: string;
      prompt: string;
      aiModelOptions?: string;
    },
    completeOnboarding: boolean = false,
  ) {
    const { aiModel, apiKey, prompt, aiModelOptions } = formData;

    try {
      // Check if settings exist for THIS session
      const existingSettings = await prisma.settingsInfo.findFirst({
        where: { sessionId: sessionId },
      });

      // Common data object for both update and create
      const data = {
        aiModel,
        apiKey,
        aiModelOptions: aiModelOptions ?? "",
        prompt,
        sessionId,
        ...(completeOnboarding && { onboardingCompleted: true }),
      };

      let settings;
      let actionType;

      if (existingSettings) {
        // Update existing settings
        settings = await prisma.settingsInfo.update({
          where: { id: existingSettings.id },
          data,
        });
        actionType = "updated";
      } else {
        // Create new settings
        settings = await prisma.settingsInfo.create({ data });
        actionType = "created";
      }

      // Customize message based on whether onboarding was completed
      const message = completeOnboarding
        ? `Settings ${actionType} and onboarding completed!`
        : `Settings ${actionType} successfully!`;

      return {
        success: true,
        settings,
        message,
      };
    } catch (error) {
      console.error("Database error:", error);
      return {
        success: false,
        error: "Database error",
        message: "Failed to save settings. Please try again.",
      };
    }
  }

  // Helper function to validate form data
  function validateFormData(
    data: FormData,
    removeIntent: boolean = false,
  ): ValidationResult {
    const entries = removeIntent
      ? Array.from(data.entries()).filter(([key]) => key !== "intent")
      : Array.from(data.entries());

    const parsed = settingFormSchema.safeParse(Object.fromEntries(entries));

    if (!parsed.success) {
      console.error(parsed.error.format());
      return {
        success: false,
        error: "Invalid form data",
        message: "Please check all required fields.",
      };
    }

    return {
      success: true,
      data: parsed.data,
    };
  }

  // Handle combined save and complete onboarding
  if (intent === "save-and-complete-onboarding") {
    // First validate the form data
    const validation = validateFormData(formData, true);

    if (!validation.success) {
      return json(validation);
    }

    // Save settings and complete onboarding
    const result = await saveSettings(session.id, validation.data, true);
    return json(result);
  }

  // Regular form submission
  const validation = validateFormData(formData);

  if (!validation.success) {
    return json(validation);
  }

  // Save settings without completing onboarding
  const result = await saveSettings(session.id, validation.data);
  return json(result);
}

export async function loader({ request }: { request: Request }) {
  const { session } = await authenticate.admin(request);

  if (!session) {
    return json({
      settings: null,
      error: "Not authenticated",
      message: "Authentication failed.",
    });
  }

  // Fetch settings for the current session/store
  const settings = await prisma.settingsInfo.findFirst({
    where: { sessionId: session.id },
  });

  return json({
    settings: settings || {
      aiModel: "",
      apiKey: "",
      aiModelOptions: "",
      prompt: "",
      onboardingCompleted: false,
    },
  });
}

export default function Setting() {
  const { settings } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const submit = useSubmit();
  const actionData = useActionData<typeof action>();

  // Use useMemo for options to optimize performance
  const aiModelSelectorMemo = useMemo(() => aiModleSelector, []);
  const keyLabelsMemo = useMemo(() => keyLabels, []);
  const aiModelOptionsMemo = useMemo(() => aiModelOptions, []);

  const [formValues, setFormValues] = useState<SettingFormSchema>({
    aiModel: settings?.aiModel || "",
    apiKey: settings?.apiKey || "",
    prompt: settings?.prompt || "",
    aiModelOptions: settings?.aiModelOptions || "",
  });

  // Show toast messages using App Bridge when form is submitted
  useEffect(() => {
    // Check if we have action data
    if (actionData) {
      // Safely access properties with type checking
      const success =
        typeof actionData === "object" && "success" in actionData
          ? Boolean(actionData.success)
          : false;

      const message =
        typeof actionData === "object" && "message" in actionData
          ? String(actionData.message)
          : success
            ? "Settings saved successfully!"
            : "Failed to save settings";

      // Use Shopify App Bridge Toast API
      if (window.shopify) {
        if (!success) {
          window.shopify.toast.show(message, { isError: true });
        } else {
          window.shopify.toast.show(message);
        }
      }
    }
  }, [actionData]);

  // Use useCallback for functions to optimize performance
  const handleChange = useCallback(
    (field: keyof SettingFormSchema) => (value: any) => {
      // If changing the AI model, also clear the API key
      if (field === "aiModel") {
        setFormValues((prev) => ({
          ...prev,
          [field]: value || " ",
          apiKey: "",
        }));
      } else {
        setFormValues((prev) => ({ ...prev, [field]: value }));
      }
    },
    [],
  );

  const handleOnboardingComplete = useCallback(() => {
    const formData = new FormData();
    formData.append("intent", "complete-onboarding");
    submit(formData, { method: "post" });
  }, [submit]);

  const handleSubmitForm = useCallback(() => {
    // Create a FormData object from the form
    const form = document.querySelector("#settings-form") as HTMLFormElement;
    if (form) {
      const formData = new FormData(form);
      // Add the onboarding completion intent to the form data
      formData.append("intent", "save-and-complete-onboarding");
      // Submit the form using Remix's submit function
      submit(formData, { method: "post" });
    }
  }, [submit]);

  // Get current API key label based on selected model
  const apiKeyLabel = useMemo(
    () =>
      formValues.aiModel
        ? keyLabelsMemo[formValues.aiModel as keyof typeof keyLabelsMemo]
        : "Enter your API Key",
    [formValues.aiModel, keyLabelsMemo],
  );

  return (
    <Page>
      <Layout>
        <Layout.Section>
          <Form data-save-bar method="post" id="settings-form">
            <BlockStack gap="400">
              <Box id="ai-model-card">
                <Card>
                  <Box paddingBlockEnd="400">
                    <Text as="h2" variant="headingLg">
                      Enter your API Keys
                    </Text>
                  </Box>
                  <Select
                    name="aiModel"
                    id="aiModel"
                    label="Select AI Model"
                    options={aiModelSelectorMemo}
                    value={formValues.aiModel}
                    onChange={handleChange("aiModel")}
                  />
                  <Box paddingBlockStart="400">
                    <TextField
                      name="apiKey"
                      id="apiKey"
                      label={apiKeyLabel}
                      value={formValues.apiKey}
                      onChange={handleChange("apiKey")}
                      autoComplete="off"
                      type="password"
                      disabled={!formValues.aiModel}
                    />
                  </Box>
                  <Box paddingBlockStart="400">
                    <Select
                      name="aiModelOptions"
                      id="aiModelOptions"
                      label="Select AI Model"
                      options={
                        formValues.aiModel
                          ? aiModelOptionsMemo[
                              formValues.aiModel as keyof typeof aiModelOptionsMemo
                            ]
                          : []
                      }
                      value={formValues.aiModelOptions}
                      onChange={handleChange("aiModelOptions")}
                      disabled={!formValues.aiModel}
                    />
                  </Box>
                </Card>
              </Box>
              <Card>
                <Box>
                  <Text as="h2" variant="headingLg">
                    Enter Your Custom Prompts
                  </Text>
                </Box>
                <Box paddingBlockStart="400">
                  <TextField
                    name="prompt"
                    id="prompt"
                    label="Enter your Custom Prompt"
                    value={formValues.prompt}
                    onChange={handleChange("prompt")}
                    autoComplete="off"
                    multiline={4}
                    type="text"
                    error={
                      formValues.prompt.length < 10
                        ? "Prompt is required"
                        : undefined
                    }
                    requiredIndicator
                  />
                </Box>
              </Card>
            </BlockStack>
            <Box paddingBlockStart="400">
              <Button
                id="submit-button"
                submit={true}
                variant="primary"
                size="large"
                loading={navigation.state === "submitting"}
              >
                Submit
              </Button>
            </Box>
          </Form>
        </Layout.Section>
      </Layout>

      {/* Onboarding Component */}
      <Onboarding
        formValues={{
          ...formValues,
          aiModelOptions: formValues.aiModelOptions || "",
        }}
        onFormChange={handleChange}
        onboardingCompleted={!!settings?.onboardingCompleted}
        onOnboardingComplete={handleOnboardingComplete}
        aiModelSelector={aiModelSelectorMemo}
        aiModelOptions={aiModelOptionsMemo}
        keyLabels={keyLabelsMemo}
        onSubmitForm={handleSubmitForm}
      />
    </Page>
  );
}
