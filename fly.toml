# fly.toml app configuration file generated for foss-engine on 2025-05-16T08:35:23+05:30
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'foss-engine'
primary_region = 'lax'

[build]

[deploy]
  release_command = 'npx prisma migrate deploy'

[env]
  PORT = '3000'
  SCOPES = 'write_products'
  SHOPIFY_API_KEY = '8a3d8bb2d7dd488e755c79c7de4a1087'
  SHOPIFY_APP_URL = 'https://app.fossengine.com'

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

[[vm]]
  memory = '2gb'
  cpu_kind = 'shared'
  cpus = 1
