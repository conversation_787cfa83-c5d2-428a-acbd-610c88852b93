import { useState, useEffect, useCallback, useMemo } from "react";
import {
  Modal,
  Text,
  BlockStack,
  Box,
  Select,
  TextField,
} from "@shopify/polaris";

// Define the props interface
type FormField = "aiModel" | "apiKey" | "prompt" | "aiModelOptions";
interface OnboardingProps {
  // Form values
  formValues: {
    aiModel: string;
    apiKey: string;
    prompt: string;
    aiModelOptions: string;
  };
  // Form change handlers
  onFormChange: (field: FormField) => (value: any) => void;
  // Whether onboarding is completed
  onboardingCompleted: boolean;
  // Callback when onboarding is finished
  onOnboardingComplete: () => void;
  // AI model options for selects
  aiModelSelector: { label: string; value: string }[];
  aiModelOptions: Record<string, { label: string; value: string }[]>;
  // API key labels based on model
  keyLabels: Record<string, string>;
  // Form submission handler
  onSubmitForm: () => void;
}

const Onboarding = ({
  formValues,
  onFormChange,
  onboardingCompleted,
  onOnboardingComplete,
  aiModelSelector,
  aiModelOptions,
  keyLabels,
  onSubmitForm,
}: OnboardingProps) => {
  // Onboarding steps configuration
  const onboardingSteps = useMemo(
    () => [
      {
        id: "welcome",
        title: "Welcome to AI Assistant Settings!",
        content:
          "Let's get your AI assistant set up. This tour will guide you through the essential settings.",
      },
      {
        id: "ai-model",
        title: "Choose Your AI Model",
        content:
          "Select the AI service you want to use - either OpenAI or DeepSeek.",
      },
      {
        id: "api-key",
        title: "Enter Your API Key",
        content:
          "Insert your API key for the selected service. This allows our app to communicate with the AI service.",
      },
      {
        id: "model-options",
        title: "Select Specific Model",
        content:
          "Choose the specific model variant you want to use (e.g., GPT-4, DeepSeek Chat).",
      },
      {
        id: "prompt",
        title: "Customize Your Prompt",
        content:
          "Enter a custom prompt to define how the AI should behave and respond.",
      },
      {
        id: "submit",
        title: "You're All Set!",
        content:
          "Click Get Started to save your settings and start using your AI assistant.",
      },
    ],
    [],
  );

  const [showOnboarding, setShowOnboarding] = useState(!onboardingCompleted);
  const [currentOnboardingStep, setCurrentOnboardingStep] = useState(0);

  // Update showOnboarding when onboardingCompleted prop changes
  useEffect(() => {
    setShowOnboarding(!onboardingCompleted);
  }, [onboardingCompleted]);

  // Use useCallback for functions to prevent unnecessary re-renders
  const handleNextStep = useCallback(() => {
    if (currentOnboardingStep < onboardingSteps.length - 1) {
      setCurrentOnboardingStep((prev) => prev + 1);
    } else {
      completeOnboarding();
    }
  }, [currentOnboardingStep, onboardingSteps.length]);

  const handlePrevStep = useCallback(() => {
    if (currentOnboardingStep > 0) {
      setCurrentOnboardingStep((prev) => prev - 1);
    }
  }, [currentOnboardingStep]);

  const completeOnboarding = useCallback(() => {
    setShowOnboarding(false);
    onOnboardingComplete();
  }, [onOnboardingComplete]);

  // Get current API key label based on selected model
  const apiKeyLabel = useMemo(() => {
    return formValues.aiModel
      ? keyLabels[formValues.aiModel as keyof typeof keyLabels]
      : "Enter your API Key";
  }, [formValues.aiModel, keyLabels]);

  // If onboarding is not shown, don't render anything
  if (!showOnboarding) {
    return null;
  }

  return (
    <Modal
      open={showOnboarding}
      onClose={completeOnboarding}
      title={onboardingSteps[currentOnboardingStep].title}
      primaryAction={{
        content:
          currentOnboardingStep === onboardingSteps.length - 1
            ? "Get Started"
            : "Next",
        onAction: () => {
          if (currentOnboardingStep === onboardingSteps.length - 1) {
            // On the last step, submit the form with the combined intent
            // This will save form values and mark onboarding as completed in one operation
            onSubmitForm();
            // The form submission will handle setting onboardingCompleted to true
            // We'll close the modal after a short delay to allow the form to submit
            setTimeout(() => {
              setShowOnboarding(false);
            }, 500);
          } else {
            handleNextStep();
          }
        },
      }}
      secondaryActions={
        currentOnboardingStep > 0
          ? [
              {
                content: "Back",
                onAction: handlePrevStep,
              },
            ]
          : undefined
      }
    >
      <Modal.Section>
        <BlockStack gap="500">
          <Text as="p" variant="bodyLg">
            {onboardingSteps[currentOnboardingStep].content}
          </Text>

          {/* Use id-based rendering instead of indices */}
          {onboardingSteps[currentOnboardingStep].id === "welcome" && (
            <Text as="p" variant="bodyMd">
              This guided tour will walk you through setting up your AI
              assistant.
            </Text>
          )}

          {onboardingSteps[currentOnboardingStep].id === "ai-model" && (
            <Select
              label="Select AI Model"
              options={aiModelSelector}
              value={formValues.aiModel}
              onChange={onFormChange("aiModel")}
            />
          )}

          {onboardingSteps[currentOnboardingStep].id === "api-key" && (
            <TextField
              label={apiKeyLabel}
              value={formValues.apiKey}
              onChange={onFormChange("apiKey")}
              autoComplete="off"
              type="password"
              disabled={!formValues.aiModel}
            />
          )}

          {onboardingSteps[currentOnboardingStep].id === "model-options" && (
            <Select
              label="Select Specific Model"
              options={
                formValues.aiModel
                  ? aiModelOptions[
                      formValues.aiModel as keyof typeof aiModelOptions
                    ]
                  : []
              }
              value={formValues.aiModelOptions}
              onChange={onFormChange("aiModelOptions")}
              disabled={!formValues.aiModel}
            />
          )}

          {onboardingSteps[currentOnboardingStep].id === "prompt" && (
            <TextField
              label="Enter your Custom Prompt"
              value={formValues.prompt}
              onChange={onFormChange("prompt")}
              autoComplete="off"
              multiline={4}
              error={
                formValues.prompt.length < 10 ? "Prompt is required" : undefined
              }
              requiredIndicator
            />
          )}

          <Box>
            <Text as="p" variant="bodyMd" tone="subdued">
              Step {currentOnboardingStep + 1} of {onboardingSteps.length}
            </Text>
          </Box>

          {/* Progress bar */}
          <Box>
            <div
              style={{
                width: "100%",
                height: "8px",
                backgroundColor: "var(--p-color-bg-surface-neutral)",
                borderRadius: "4px",
              }}
            >
              <div
                style={{
                  width: `${
                    ((currentOnboardingStep + 1) / onboardingSteps.length) * 100
                  }%`,
                  height: "100%",
                  backgroundColor: "var(--p-color-bg-fill-info)",
                  borderRadius: "4px",
                  transition: "width 0.3s ease",
                }}
              />
            </div>
          </Box>

          {onboardingSteps[currentOnboardingStep].id === "submit" && (
            <Box
              borderColor="border-info"
              borderWidth="025"
              padding="400"
              borderRadius="200"
            >
              <Text as="p" variant="bodyMd" tone="success">
                🎉 You're ready to use your AI assistant! Click "Get Started" to
                begin.
              </Text>
            </Box>
          )}
        </BlockStack>
      </Modal.Section>
    </Modal>
  );
};

export default Onboarding;
