import {
  json,
  type LoaderFunctionArgs,
  type ActionFunctionArgs,
} from "@remix-run/node";
import prisma from "app/db.server";
import { corsHeaders, handleOptionsRequest } from "./Utility/cors";
import {
  successResponse,
  errorResponse,
  notFoundResponse,
  serverErrorResponse,
} from "./Utility/responses";
import { getSessionByShop } from "./Utility/session";

// Define the helper function to transform data
interface ProductDataInput {
  id: string;
  title: string;
  content?: string;
  media?: any[];
  metafields?: any[];
  shopUrl: string;
}

interface TransformedProductData {
  productId: string;
  title: string;
  description: string;
  image: any[];
  mataField: any[];
  shopUrl: string;
}

const transformProductData = (
  data: ProductDataInput,
): TransformedProductData => {
  return {
    productId: data.id,
    title: data.title,
    description: data.content || "", // Frontend: content → DB: description
    image: data.media || [], // Frontend: media → DB: image
    mataField: data.metafields || [], // Frontend: metafields → DB: mataField
    shopUrl: data.shopUrl, // Frontend: shopUrl → DB: shopUrl
  };
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "OPTIONS") {
    return handleOptionsRequest();
  }

  return json(
    { message: "Hello from product Data api!" },
    {
      status: 200,
      headers: corsHeaders,
    },
  );
};

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    // Parse and validate incoming data
    const productData = await request.json();

    // Transform data
    const transformedData = transformProductData(productData);
    // Check if product already exists
    const existingProduct = await prisma.productInfo.findUnique({
      where: { productId: transformedData.productId },
    });

    // Option 1: Throw error if product exists
    if (existingProduct) {
      return errorResponse("Product already exists in database", 409);
    }

    // Get session
    const session = await getSessionByShop(transformedData.shopUrl);

    if (!session) {
      return notFoundResponse("Session not found");
    }

    // Create new product (only if it doesn't exist)
    const productInfo = await prisma.productInfo.create({
      data: {
        productId: transformedData.productId,
        title: transformedData.title,
        description: transformedData.description,
        image: transformedData.image,
        mataField: transformedData.mataField,
        sessionId: session.id,
      },
    });

    return successResponse(productInfo, "Product created", 201);
  } catch (error) {
    return serverErrorResponse(error);
  }
};
