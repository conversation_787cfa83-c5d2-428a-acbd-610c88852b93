/**
 * A simple in-memory cache utility
 */
export class MemoryCache<T> {
  private cache = new Map<string, { data: T; expires: number }>();
  private defaultTtl: number;

  /**
   * @param defaultTtlMinutes Default time-to-live in minutes
   */
  constructor(defaultTtlMinutes: number = 10) {
    this.defaultTtl = defaultTtlMinutes * 60 * 1000;
  }

  /**
   * Set a value in the cache
   */
  set(key: string, value: T, ttlMinutes?: number): void {
    const ttl = (ttlMinutes ?? this.defaultTtl / 60000) * 60 * 1000;
    const expires = Date.now() + ttl;

    this.cache.set(key, { data: value, expires });

    // Set automatic cleanup
    setTimeout(() => {
      this.delete(key);
    }, ttl);
  }

  /**
   * Get a value from the cache
   */
  get(key: string): T | null {
    const item = this.cache.get(key);

    if (!item) return null;
    if (item.expires < Date.now()) {
      this.delete(key);
      return null;
    }

    return item.data;
  }

  /**
   * Delete a value from the cache
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear the entire cache
   */
  clear(): void {
    this.cache.clear();
  }
}

// Create and export default cache instances
export const responseCache = new MemoryCache<any>(10); // 10 minutes TTL
