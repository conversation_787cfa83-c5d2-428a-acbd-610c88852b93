import type { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import db from "../db.server";
import { verifyWebhookHmac } from "./Utility/verifyHmac";

export const action = async ({ request }: ActionFunctionArgs) => {
  const rawBody = await request.text();
  const hmacHeader = request.headers.get("X-Shopify-Hmac-Sha256");
  const secret = process.env.SHOPIFY_API_SECRET!;

  const isVerified = verifyWebhookHmac({
    rawBody,
    hmacHeader,
    secret,
  });

  if (!isVerified) return new Response("Unauthorized", { status: 401 });

  const { shop, session, topic } = await authenticate.webhook(
    new Request(request.url, {
      method: request.method,
      headers: request.headers,
      body: rawBody,
    })
  );

  if (session) {
    await db.session.deleteMany({ where: { shop } });
  }

  return new Response("OK");
};
