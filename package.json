{"name": "foss-engine", "private": true, "scripts": {"build": "remix vite:build", "dev": "shopify app dev", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.11", "@ai-sdk/google": "^1.2.18", "@ai-sdk/openai": "^1.3.22", "@prisma/client": "^6.8.0", "@remix-run/dev": "^2.16.6", "@remix-run/fs-routes": "^2.16.6", "@remix-run/node": "^2.16.6", "@remix-run/react": "^2.16.6", "@remix-run/serve": "^2.16.6", "@shopify/app-bridge-react": "^4.1.10", "@shopify/polaris": "^13.9.5", "@shopify/shopify-app-remix": "^3.8.2", "@shopify/shopify-app-session-storage-prisma": "^6.0.6", "ai": "^4.3.15", "isbot": "^5.1.27", "react": "^18.3.1", "react-dom": "^18.3.1", "vite-tsconfig-paths": "^5.1.4", "zod": "^3.24.4"}, "devDependencies": {"@flydotio/dockerfile": "^0.7.10", "@remix-run/eslint-config": "^2.16.6", "@remix-run/route-config": "^2.16.6", "@shopify/api-codegen-preset": "^1.1.7", "@types/eslint": "^9.6.1", "@types/node": "^22.15.17", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "prettier": "^3.5.3", "prisma": "^6.8.0", "typescript": "^5.8.3", "vite": "^6.3.5"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0", "minimatch": "9.0.5"}, "overrides": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0", "minimatch": "9.0.5"}, "author": "<PERSON><PERSON>"}