import type { ActionFunctionArgs } from "@remix-run/node";
import db from "../db.server";
import { authenticate } from "../shopify.server";


export const action = async ({ request }: ActionFunctionArgs) => {
  const payload = await request.json();
  console.log("SHOP_DATA_ERASE received", payload);

  // Remove all records related to the shop
  const shopDomain = payload.shop_domain;
  await db.session.deleteMany({ where: { shop: shopDomain } });

  return new Response("OK");
};
