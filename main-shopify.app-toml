# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "8a3d8bb2d7dd488e755c79c7de4a1087"
name = "Foss Engine"
handle = "foss-engine"
application_url = "https://app.fossengine.com/"
embedded = true

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = [
  "https://app.fossengine.com/auth/callback",
  "https://app.fossengine.com/auth/shopify/callback",
  "https://app.fossengine.com/api/auth/callback"
]

[pos]
embedded = false
