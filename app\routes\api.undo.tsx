import {
  json,
  type LoaderFunctionArgs,
  type ActionFunctionArgs,
} from "@remix-run/node";
import prisma from "app/db.server";
import { corsHeaders, handleOptionsRequest } from "./Utility/cors";
import {
  successResponse,
  errorResponse,
  notFoundResponse,
  serverErrorResponse,
} from "./Utility/responses";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "OPTIONS") {
    return handleOptionsRequest();
  }

  return json(
    { message: "Hello from undu!" },
    {
      status: 200,
      headers: corsHeaders,
    },
  );
};

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const undoData = await request.json();
    // get the productInfo data with the product id for the request
    const productInfo = await prisma.productInfo.findFirst({
      where: {
        productId: undoData.productId,
      },
    });
    // Check if the product info exists
    if (!productInfo) {
      return notFoundResponse("Product not found");
    }
    // if we have the product info we will send the prduct info
    return successResponse(productInfo, "Product found");
  } catch (error) {
    return serverErrorResponse(error);
  }
};
