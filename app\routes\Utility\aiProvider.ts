import { openai } from "@ai-sdk/openai";
import { anthropic } from "@ai-sdk/anthropic";
import { google, createGoogleGenerativeAI } from "@ai-sdk/google";
import { generateObject, generateText, CoreMessage } from "ai";

// Define types for multi-modal content
type TextPart = {
  type: "text";
  text: string;
};

type ImagePart = {
  type: "image";
  image: string;
};

/**
 * Extract image URLs from image data
 * This function handles various image data formats:
 * - Single URL string
 * - Array of URL strings
 * - Array of objects with src property (e.g., [{src: "https://..."}])
 * - Complex nested objects containing URLs
 */
function extractImageUrls(imageData: unknown): string[] {
  if (!imageData) return [];

  try {
    // If it's already a string URL, return it directly
    if (
      typeof imageData === "string" &&
      imageData.match(/https?:\/\/[^\s"']+\.(jpg|jpeg|png|gif|webp)/i)
    ) {
      return [imageData];
    }

    // If it's an array, process each item
    if (Array.isArray(imageData)) {
      const urls: string[] = [];

      for (const item of imageData) {
        // If item is a string URL
        if (
          typeof item === "string" &&
          item.match(/https?:\/\/[^\s"']+\.(jpg|jpeg|png|gif|webp)/i)
        ) {
          urls.push(item);
        }
        // If item is an object with src property (common image object format)
        else if (
          item &&
          typeof item === "object" &&
          "src" in item &&
          typeof item.src === "string" &&
          item.src.match(/https?:\/\/[^\s"']+\.(jpg|jpeg|png|gif|webp)/i)
        ) {
          urls.push(item.src);
        }
        // If item is an object with url property
        else if (
          item &&
          typeof item === "object" &&
          "url" in item &&
          typeof item.url === "string" &&
          item.url.match(/https?:\/\/[^\s"']+\.(jpg|jpeg|png|gif|webp)/i)
        ) {
          urls.push(item.url);
        }
        // For other object types, convert to string and extract URLs
        else if (item && typeof item === "object") {
          const itemStr = JSON.stringify(item);
          const matches = itemStr.match(
            /https?:\/\/[^\s"']+\.(jpg|jpeg|png|gif|webp)/gi,
          );
          if (matches) {
            urls.push(...matches);
          }
        }
      }

      return [...new Set(urls)]; // Remove duplicates
    }

    // For other object types, convert to string and extract URLs
    const imageStr =
      typeof imageData === "string" ? imageData : JSON.stringify(imageData);
    const urlRegex = /https?:\/\/[^\s"']+\.(jpg|jpeg|png|gif|webp)/gi;
    return [...new Set(imageStr.match(urlRegex) || [])]; // Remove duplicates
  } catch (error) {
    return [];
  }
}
/**
 * Interface for AI settings
 */
interface AISettings {
  aiModel: string;
  apiKey: string;
  aiModelOptions?: string;
}

/**
 * Interface for generation options
 */
interface GenerationOptions {
  systemPrompt: string;
  userPrompt: string;
  isRegeneration: boolean;
  schema?: any; // The schema must be provided by the API endpoint
  imageData?: unknown; // Optional image data that can be processed
}

/**
 * Creates the appropriate chat model based on the provider
 */
const createChatModel = (settings: AISettings) => {
  // Set environment variables for API keys
  // This is a workaround since the Vercel AI SDK reads from environment variables
  process.env.OPENAI_API_KEY = settings.apiKey;
  process.env.ANTHROPIC_API_KEY = settings.apiKey;
  process.env.GOOGLE_GENERATIVE_AI_API_KEY = settings.apiKey;

  switch (settings.aiModel) {
    case "openai":
      // Use the model ID directly
      return openai(settings.aiModelOptions || "gpt-4o");

    case "claude":
      // Use the model ID directly
      return anthropic(settings.aiModelOptions || "claude-3-5-sonnet-20240620");

    case "gemini":
      // Create a Google provider instance
      const googleProvider = createGoogleGenerativeAI();
      // Return the model with the specified model ID
      return googleProvider(settings.aiModelOptions || "gemini-2.0-flash");

    default:
      throw new Error("Unsupported AI model provider");
  }
};

/**
 * Process content with AI provider using structured output
 */
export async function aiProvider(
  settings: AISettings,
  options: GenerationOptions,
) {
  try {

    // Create the appropriate chat model
    const model = createChatModel(settings);

    const imageUrls = options.imageData
      ? extractImageUrls(options.imageData)
      : [];

    // Prepare messages for Vercel AI SDK
    let userMessage: CoreMessage;

    if (imageUrls.length > 0) {
      // If image URLs are found, create a multi-modal message with content parts
      const contentParts: Array<TextPart | ImagePart> = [
        { type: "text", text: options.userPrompt },
      ];

      // Add image parts - each URL becomes a separate image part
      imageUrls.forEach((url) => {
        contentParts.push({ type: "image", image: url });
      });

      userMessage = { role: "user", content: contentParts } as CoreMessage;
    } else {
      // Regular text message
      userMessage = {
        role: "user",
        content: options.userPrompt,
      } as CoreMessage;
    }

    // Convert our messages to the format expected by the AI SDK
    const messages = [
      { role: "system", content: options.systemPrompt },
      userMessage,
    ] as CoreMessage[];

    // Set temperature based on regeneration flag
    const temperature = options.isRegeneration ? 0.9 : 0.7;

    // Check if a schema is provided
    if (options.schema) {
      // Use generateObject with the provided schema
      const response = await generateObject({
        model,
        messages,
        schema: options.schema,
        temperature,
        maxTokens: 1000,
      });


      return {
        success: true,
        data: response.object,
      };
    } else {
      // If no schema is provided, use regular text generation
      const response = await generateText({
        model,
        messages,
        temperature,
        maxTokens: 1000,
      });

      // Extract the content
      const content = response.text;

      // Try to parse as JSON if it looks like JSON
      try {
        if (content.trim().startsWith("{") && content.trim().endsWith("}")) {
          const parsedContent = JSON.parse(
            content.replace(/^```json\s*|\s*```$/g, "").trim(),
          );

          return {
            success: true,
            data: parsedContent,
          };
        }

        // Return raw content if not JSON
        return {
          success: true,
          data: { raw: content },
        };
      } catch (parseError) {
        return {
          success: true,
          data: { raw: content, parseError: true },
        };
      }
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return {
      success: false,
      error: errorMessage,
    };
  }
}
