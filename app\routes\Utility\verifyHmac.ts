import crypto from "crypto";

export function verifyWebhookHmac({
  rawBody,
  hmacHeader,
  secret,
}: {
  rawBody: string;
  hmacHeader: string | null;
  secret: string;
}): boolean {
  if (!hmacHeader) return false;

  const generated = crypto
    .createHmac("sha256", secret)
    .update(rawBody, "utf8")
    .digest("base64");

  return crypto.timingSafeEqual(Buffer.from(hmacHeader), Buffer.from(generated));
}
